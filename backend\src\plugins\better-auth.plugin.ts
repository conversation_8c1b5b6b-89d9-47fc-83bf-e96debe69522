import { FastifyInstance } from 'fastify';
import fp from 'fastify-plugin';
import { auth } from '@/lib/auth';
import { validateEnv } from '@/config/env';

async function betterAuthPlugin(fastify: FastifyInstance) {
  const env = validateEnv();

  // Add CORS headers specifically for auth routes
  fastify.addHook('onRequest', async (request, reply) => {
    if (request.url.startsWith('/api/auth')) {
      reply.header('Access-Control-Allow-Origin', env.FRONTEND_URL);
      reply.header('Access-Control-Allow-Credentials', 'true');
      reply.header(
        'Access-Control-Allow-Methods',
        'GET, POST, PUT, DELETE, OPTIONS'
      );
      reply.header(
        'Access-Control-Allow-Headers',
        'Content-Type, Authorization, Cookie'
      );

      if (request.method === 'OPTIONS') {
        reply.status(200).send();
        return;
      }
    }
  });

  // Register Better Auth handler for all auth routes
  fastify.register(async function (fastify) {
    fastify.all('/api/auth/*', async (request, reply) => {
      console.log('Auth route hit:', request.method, request.url);
      console.log('Query params:', request.query);

      // Create proper Web Request object
      const protocol = request.headers['x-forwarded-proto'] || 'http';
      const host = request.headers.host;
      const fullUrl = `${protocol}://${host}${request.url}`;

      let body: string | undefined = undefined;
      if (
        request.method !== 'GET' &&
        request.method !== 'HEAD' &&
        request.body
      ) {
        body = JSON.stringify(request.body);
      }

      const requestInit: RequestInit = {
        method: request.method,
        headers: request.headers as Record<string, string>,
      };

      if (body) {
        requestInit.body = body;
      }

      const webRequest = new Request(fullUrl, requestInit);

      const response = await auth.handler(webRequest);

      // Check if this is a redirect response
      if (response.status >= 300 && response.status < 400) {
        const location = response.headers.get('location');
        console.log('Redirect detected:', location);

        // If the redirect is to the backend, redirect to frontend instead
        if (location && location.startsWith('http://localhost:3001')) {
          // Check if there's a callbackURL in the original request
          const callbackURL = (request.query as any)?.callbackURL;
          const frontendUrl =
            callbackURL || env.FRONTEND_URL || 'http://localhost:5173';
          console.log('Redirecting to frontend:', frontendUrl);
          return reply.redirect(frontendUrl);
        }
      }

      // Special handling for successful OAuth callback
      if (request.url.includes('/callback/') && response.status === 200) {
        console.log('OAuth callback detected, redirecting to frontend');
        const frontendUrl = env.FRONTEND_URL || 'http://localhost:5173';
        return reply.redirect(frontendUrl);
      }

      // Set status and headers
      reply.status(response.status);
      response.headers.forEach((value, key) => {
        reply.header(key, value);
      });

      // Return response body
      return response.text();
    });
  });

  // Add auth helper to request
  fastify.addHook('preHandler', async request => {
    if (!request.url.startsWith('/api/auth')) {
      (request as any).auth = auth;
    }
  });
}

export default fp(betterAuthPlugin, {
  name: 'better-auth',
});

declare module 'fastify' {
  interface FastifyRequest {
    auth: typeof auth;
  }
}
