import { FastifyInstance } from 'fastify';
import fp from 'fastify-plugin';
import { auth } from '@/lib/auth';

async function betterAuthPlugin(fastify: FastifyInstance) {
  fastify.all('/api/auth/*', async (request, reply) => {
    const url = new URL(request.url, `http://${request.headers.host}`);

    const webRequest = new Request(url, {
      method: request.method,
      headers: request.headers as Record<string, string>,
      body:
        request.method !== 'GET' && request.body
          ? JSON.stringify(request.body)
          : undefined,
    });

    const response = await auth.handler(webRequest);

    reply.status(response.status);
    response.headers.forEach((value, key) => reply.header(key, value));
    return response.text();
  });

  fastify.addHook('preHandler', async request => {
    (request as any).auth = auth;
  });
}

export default fp(betterAuthPlugin, {
  name: 'better-auth',
});

declare module 'fastify' {
  interface FastifyRequest {
    auth: typeof auth;
  }
}
