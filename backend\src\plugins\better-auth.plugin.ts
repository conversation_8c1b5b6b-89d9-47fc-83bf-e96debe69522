import { FastifyInstance } from 'fastify';
import fp from 'fastify-plugin';
import { auth } from '@/lib/auth';

async function betterAuthPlugin(fastify: FastifyInstance) {
  // Add CORS headers specifically for auth routes
  fastify.addHook('onRequest', async (request, reply) => {
    if (request.url.startsWith('/api/auth')) {
      reply.header('Access-Control-Allow-Origin', 'http://localhost:5173');
      reply.header('Access-Control-Allow-Credentials', 'true');
      reply.header(
        'Access-Control-Allow-Methods',
        'GET, POST, PUT, DELETE, OPTIONS'
      );
      reply.header(
        'Access-Control-Allow-Headers',
        'Content-Type, Authorization, Cookie'
      );

      if (request.method === 'OPTIONS') {
        reply.status(200).send();
        return;
      }
    }
  });

  // Register Better Auth handler for all auth routes
  fastify.register(async function (fastify) {
    fastify.all('/api/auth/*', async (request, reply) => {
      console.log('Auth route hit:', request.method, request.url);

      // Create proper Web Request object
      const protocol = request.headers['x-forwarded-proto'] || 'http';
      const host = request.headers.host;
      const fullUrl = `${protocol}://${host}${request.url}`;

      let body = undefined;
      if (
        request.method !== 'GET' &&
        request.method !== 'HEAD' &&
        request.body
      ) {
        body = JSON.stringify(request.body);
      }

      const webRequest = new Request(fullUrl, {
        method: request.method,
        headers: request.headers as HeadersInit,
        body,
      });

      const response = await auth.handler(webRequest);

      // Set status and headers
      reply.status(response.status);
      response.headers.forEach((value, key) => {
        reply.header(key, value);
      });

      // Return response body
      return response.text();
    });
  });

  // Add auth helper to request
  fastify.decorateRequest('auth', null);
  fastify.addHook('preHandler', async request => {
    if (!request.url.startsWith('/api/auth')) {
      request.auth = auth;
    }
  });
}

export default fp(betterAuthPlugin, {
  name: 'better-auth',
});

declare module 'fastify' {
  interface FastifyRequest {
    auth: typeof auth;
  }
}
